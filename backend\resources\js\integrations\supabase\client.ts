// This file is automatically generated. Do not edit it directly.
import { SupabaseClient } from '@supabase/supabase-js'; // Tidak perlu createClient jika kita selalu mocking

// Fungsi untuk membuat klien Supabase mock
function createMockSupabaseClient(): SupabaseClient {
  return {
    auth: {
      onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } }),
      signInWithPassword: async () => ({ data: { user: null, session: null }, error: new Error("Supabase tidak dikonfigurasi. Menggunakan autentikasi lokal.") }),
      signOut: async () => ({ error: null }),
      // Tambahkan metode auth lain jika digunakan di tempat lain dan perlu dimock
    },
    from: () => ({
      select: () => ({
        eq: () => ({
          single: async () => ({ data: null, error: new Error("Supabase tidak dikonfigurasi. Menggunakan penyimpanan data lokal.") })
        })
      })
    }),
    // Tambahkan properti klien Supabase tingkat atas lainnya jika digunakan di tempat lain dan perlu dimock
    // contoh: storage, functions, rpc
  } as unknown as SupabaseClient; // Cast ke unknown dulu untuk memenuhi pemeriksaan tipe
}

// Selalu ekspor klien mock jika Supabase tidak diinginkan.
// Ini memastikan tidak ada klien Supabase yang sebenarnya pernah dibuat, mencegah kesalahan 'supabaseUrl is required'.
export const supabase: SupabaseClient = createMockSupabaseClient();

console.warn("Integrasi Supabase dinonaktifkan. Menggunakan autentikasi dan penyimpanan data lokal.");