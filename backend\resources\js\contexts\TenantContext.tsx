import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';
import { Tenant } from '../types';

interface TenantContextType {
  tenants: Tenant[];
  activeTenantId: string | null;
  addTenant: (name: string, domain?: string) => void; // Updated signature
  deleteTenant: (tenantId: string) => void;
  selectTenant: (tenantId: string) => void;
  renameTenant: (tenantId: string, newName: string) => void;
  getActiveTenant: () => Tenant | null;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export const useTenant = () => {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
};

const defaultTenants: Tenant[] = [
  { id: 'default-tenant', name: 'Default Hotspot' },
];

export const TenantProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [tenants, setTenants] = useState<Tenant[]>(() => {
    try {
      const savedTenants = localStorage.getItem('app-tenants');
      return savedTenants ? JSON.parse(savedTenants) : defaultTenants;
    } catch {
      return defaultTenants;
    }
  });

  const [activeTenantId, setActiveTenantId] = useState<string | null>(() => {
    try {
      const savedActiveTenantId = localStorage.getItem('active-tenant-id');
      // Ensure the saved active tenant ID actually exists in the current tenants list
      if (savedActiveTenantId && tenants.some(t => t.id === savedActiveTenantId)) {
        return savedActiveTenantId;
      }
      return defaultTenants[0].id; // Default to the first tenant
    } catch {
      return defaultTenants[0].id;
    }
  });

  useEffect(() => {
    localStorage.setItem('app-tenants', JSON.stringify(tenants));
  }, [tenants]);

  useEffect(() => {
    if (activeTenantId) {
      localStorage.setItem('active-tenant-id', activeTenantId);
    } else {
      localStorage.removeItem('active-tenant-id');
    }
  }, [activeTenantId]);

  const addTenant = useCallback((name: string, domain?: string) => { // Added domain parameter
    const newTenant: Tenant = {
      id: `tenant-${Date.now()}`,
      name,
      domain, // Include domain
    };
    setTenants((prev) => [...prev, newTenant]);
    toast.success(`Tenant '${name}' added successfully!`);
  }, []);

  const deleteTenant = useCallback((tenantId: string) => {
    if (tenants.length === 1) {
      toast.error('Cannot delete the last tenant.');
      return;
    }
    setTenants((prev) => {
      const updatedTenants = prev.filter((t) => t.id !== tenantId);
      if (activeTenantId === tenantId) {
        setActiveTenantId(updatedTenants[0]?.id || null);
      }
      return updatedTenants;
    });
    toast.success('Tenant deleted successfully!');
  }, [tenants, activeTenantId]);

  const selectTenant = useCallback((tenantId: string) => {
    if (tenants.some(t => t.id === tenantId)) {
      setActiveTenantId(tenantId);
      toast.success(`Switched to tenant: ${tenants.find(t => t.id === tenantId)?.name}`);
    } else {
      toast.error('Tenant not found.');
    }
  }, [tenants]);

  const renameTenant = useCallback((tenantId: string, newName: string) => {
    setTenants(prev => prev.map(tenant => 
      tenant.id === tenantId ? { ...tenant, name: newName } : tenant
    ));
    toast.success(`Tenant name updated to '${newName}'!`);
  }, []);

  const getActiveTenant = useCallback(() => {
    return tenants.find(t => t.id === activeTenantId) || null;
  }, [tenants, activeTenantId]);

  const value = {
    tenants,
    activeTenantId,
    addTenant,
    deleteTenant,
    selectTenant,
    renameTenant,
    getActiveTenant,
  };

  return (
    <TenantContext.Provider value={value}>
      {children}
    </TenantContext.Provider>
  );
};